<template>
  <div class="search-container">
    <!-- Logo区域 -->
    <div class="logo-section">
      <h1 class="logo-text">请输入要生成条码的销售单号</h1>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-box">
        <n-input
          v-model:value="searchQuery"
          size="large"
          placeholder="搜索任何内容..."
          clearable
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <n-icon :size="20" class="search-icon">
              <SearchIcon />
            </n-icon>
          </template>
        </n-input>
      </div>

      <!-- 搜索按钮 -->
      <div class="button-section">
        <n-space :size="16">
         <n-button type="info"             @click="handleSearch"
            :disabled="!searchQuery.trim()">
      {{ '查询' }}
    </n-button>
          
        </n-space>
      </div>
    </div>

    
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search24Regular as SearchIcon } from '@vicons/fluent'

// 搜索查询字符串
const searchQuery = ref('')

// 处理搜索
const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  console.log('搜索内容:', searchQuery.value)
  // 这里可以添加实际的搜索逻辑
  // 例如：调用搜索API、跳转到搜索结果页面等
}

</script>

<style scoped>
.search-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.logo-section {
  margin-bottom: 40px;
}

.logo-text {
  font-size: 4rem;
  font-weight: 300;
  color: #4285f4;
  margin: 0;
  text-align: center;
  letter-spacing: -2px;
}

.search-section {
  width: 100%;
  max-width: 600px;
  margin-bottom: 40px;
}

.search-box {
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.search-input {
  border-radius: 24px !important;
}

.search-input :deep(.n-input__input-el) {
  padding: 12px 16px;
  font-size: 16px;
}

.search-input :deep(.n-input__border) {
  border: none;
}

.search-input :deep(.n-input__state-border) {
  border: none;
}

.search-icon {
  color: #9aa0a6;
  margin-left: 8px;
}

.button-section {
  display: flex;
  justify-content: center;
}

.search-button {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 100px;
}

.search-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.quick-links {
  margin-top: 30px;
}

.quick-links :deep(.n-button) {
  color: #1a73e8;
  font-size: 14px;
}

.quick-links :deep(.n-button:hover) {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-text {
    font-size: 3rem;
  }

  .search-section {
    max-width: 90%;
  }

  .button-section :deep(.n-space) {
    flex-direction: column;
    align-items: center;
  }

  .search-button {
    width: 200px;
  }
}
</style>
